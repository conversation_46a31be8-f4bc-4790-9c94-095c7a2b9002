// Simple test script to check storage bucket contents
// Run this in browser console on the app page

async function debugStorageContents() {
  console.log('=== Storage Debug Test ===');
  
  try {
    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError) {
      console.error('User error:', userError);
      return;
    }
    
    if (!user) {
      console.error('No user logged in');
      return;
    }
    
    console.log('User ID:', user.id);
    
    // List buckets
    const { data: buckets, error: bucketError } = await supabase.storage.listBuckets();
    console.log('Buckets:', buckets, 'Error:', bucketError);
    
    // List root of certificateuploads bucket
    const { data: rootFiles, error: rootError } = await supabase.storage
      .from('certificateuploads')
      .list('', { limit: 20 });
    console.log('Root files:', rootFiles, 'Error:', rootError);
    
    // List user directory
    const { data: userFiles, error: userError2 } = await supabase.storage
      .from('certificateuploads')
      .list(user.id, { limit: 20 });
    console.log('User directory files:', userFiles, 'Error:', userError2);
    
    // Get active certificate ID from context (if available)
    const activeCertificateId = window.activeCertificateId || 'test-certificate-id';
    console.log('Active certificate ID:', activeCertificateId);
    
    // List certificate directory
    const certificatePath = `${user.id}/${activeCertificateId}`;
    const { data: certFiles, error: certError } = await supabase.storage
      .from('certificateuploads')
      .list(certificatePath, { limit: 20 });
    console.log('Certificate directory files:', certFiles, 'Error:', certError);
    
  } catch (error) {
    console.error('Debug test error:', error);
  }
}

// Run the test
debugStorageContents();
