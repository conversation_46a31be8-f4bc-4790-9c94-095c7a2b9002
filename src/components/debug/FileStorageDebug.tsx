import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '../../lib/supabase';
import { listCertificateFiles } from '../../utils/fileUtils';
import { useCertificate } from '../../contexts/CertificateContext';

/**
 * Debug component to investigate file storage issues
 */
export const FileStorageDebug = () => {
  const { activeCertificateId } = useCertificate();
  const [debugInfo, setDebugInfo] = useState<any>(null);

  // Get current user
  const { data: user } = useQuery({
    queryKey: ['user'],
    queryFn: async () => {
      const { data: { user } } = await supabase.auth.getUser();
      return user;
    },
  });

  // Test file listing
  const testFileListing = async () => {
    if (!user?.id || !activeCertificateId) {
      setDebugInfo({ error: 'No user or certificate ID available' });
      return;
    }

    try {
      console.log('Testing file listing for:', { userId: user.id, certificateId: activeCertificateId });
      
      // Test direct storage listing
      const directoryPath = `${user.id}/${activeCertificateId}`;
      const { data: storageData, error: storageError } = await supabase.storage
        .from('certificateuploads')
        .list(directoryPath, {
          limit: 100,
          sortBy: { column: 'name', order: 'asc' }
        });

      // Test utility function
      const utilityFiles = await listCertificateFiles(user.id, activeCertificateId);

      setDebugInfo({
        userId: user.id,
        certificateId: activeCertificateId,
        directoryPath,
        storageData,
        storageError,
        utilityFiles,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('Debug test error:', error);
      setDebugInfo({ 
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      });
    }
  };

  // Test storage bucket listing
  const testBucketListing = async () => {
    try {
      const { data: buckets, error } = await supabase.storage.listBuckets();
      
      if (error) {
        setDebugInfo({ bucketError: error, timestamp: new Date().toISOString() });
        return;
      }

      // Try to list root of certificateuploads bucket
      const { data: rootFiles, error: rootError } = await supabase.storage
        .from('certificateuploads')
        .list('', { limit: 10 });

      setDebugInfo({
        buckets,
        rootFiles,
        rootError,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('Bucket test error:', error);
      setDebugInfo({ 
        bucketTestError: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      });
    }
  };

  return (
    <div className="bg-gray-100 p-4 rounded-lg">
      <h3 className="text-lg font-semibold mb-4">File Storage Debug</h3>
      
      <div className="space-y-2 mb-4">
        <button
          onClick={testFileListing}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          disabled={!user?.id || !activeCertificateId}
        >
          Test File Listing
        </button>
        
        <button
          onClick={testBucketListing}
          className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 ml-2"
        >
          Test Bucket Listing
        </button>
      </div>

      <div className="text-sm">
        <p><strong>User ID:</strong> {user?.id || 'Not available'}</p>
        <p><strong>Certificate ID:</strong> {activeCertificateId || 'Not available'}</p>
      </div>

      {debugInfo && (
        <div className="mt-4">
          <h4 className="font-semibold mb-2">Debug Results:</h4>
          <pre className="bg-white p-3 rounded text-xs overflow-auto max-h-96">
            {JSON.stringify(debugInfo, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
};
